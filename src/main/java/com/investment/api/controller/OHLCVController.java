package com.investment.api.controller;

import com.investment.api.model.ApiResponse;
import com.investment.api.model.OHLCVResponse;
import com.investment.api.model.RefreshAllRequest;
import com.investment.api.model.RefreshAllResponse;
import com.investment.api.model.UpdateRequest;
import com.investment.api.model.UpdateResponse;
import com.investment.model.OHLCV;
import com.investment.service.OHLCVService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * REST controller for OHLCV data operations.
 */
@RestController
@RequestMapping("/api/ohlcv")
@Tag(name = "OHLCV", description = "OHLCV data operations")
public class OHLCVController {
    private static final Logger logger = LoggerFactory.getLogger(OHLCVController.class);

    private OHLCVService ohlcvService;

    public OHLCVController(OHLCVService ohlcvService) {
        this.ohlcvService = ohlcvService;
    }

    /**
     * Get OHLCV data for a specific symbol.
     *
     * @param symbol The stock symbol
     * @param startDate Optional start date (defaults to 30 days ago)
     * @param endDate Optional end date (defaults to today)
     * @return OHLCV data for the specified symbol and date range
     */
    @GetMapping("/{symbol}")
    @Operation(summary = "Get OHLCV data for a specific symbol",
               description = "Retrieves OHLCV (Open, High, Low, Close, Volume) data for a specific stock symbol within a date range")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "No data found for the symbol"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<OHLCVResponse>>> getOHLCVData(
            @Parameter(description = "Stock symbol", required = true)
            @PathVariable String symbol,

            @Parameter(description = "Start date (yyyy-MM-dd)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,

            @Parameter(description = "End date (yyyy-MM-dd)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            // Set default dates if not provided
            LocalDate start = (startDate != null) ? startDate : LocalDate.now().minusDays(30);
            LocalDate end = (endDate != null) ? endDate : LocalDate.now();

            logger.info("Retrieving OHLCV data for symbol: {}, from {} to {}", symbol, start, end);

            List<OHLCV> ohlcvData = ohlcvService.getOHLCVData(symbol, start, end);

            if (ohlcvData.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("No data found for symbol: " + symbol));
            }

            List<OHLCVResponse> responseData = ohlcvData.stream()
                    .map(OHLCVResponse::new)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success(responseData));
        } catch (Exception e) {
            logger.error("Error retrieving OHLCV data for symbol: {}", symbol, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve OHLCV data: " + e.getMessage()));
        }
    }

    /**
     * Update OHLCV data for specified symbols.
     *
     * @param request Update request containing symbols to update
     * @return Update response with count of updated symbols
     */
    @PostMapping("/update")
    @Operation(summary = "Update OHLCV data",
               description = "Triggers an update of OHLCV data for specified symbols")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully updated data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<UpdateResponse>> updateOHLCVData(
            @Parameter(description = "Update request with symbols to update", required = true)
            @RequestBody UpdateRequest request) {

        try {
            if (request.getSymbols() == null || request.getSymbols().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("No symbols provided for update"));
            }

            logger.info("Updating OHLCV data for {} symbols", request.getSymbols().size());

            int updatedCount = ohlcvService.updateOHLCVDataBatch(request.getSymbols());

            UpdateResponse response = new UpdateResponse(
                    updatedCount,
                    String.format("Updated data for %d out of %d symbols",
                            updatedCount, request.getSymbols().size())
            );

            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            logger.error("Error updating OHLCV data", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update OHLCV data: " + e.getMessage()));
        }
    }

    /**
     * Update OHLCV data for a single symbol.
     *
     * @param symbol The stock symbol to update
     * @return Update response with count of updated data points
     */
    @PostMapping("/update/{symbol}")
    @Operation(summary = "Update OHLCV data for a specific symbol",
               description = "Triggers an update of OHLCV data for a specific stock symbol")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully updated data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<UpdateResponse>> updateSymbolData(
            @Parameter(description = "Stock symbol", required = true)
            @PathVariable String symbol) {

        try {
            logger.info("Updating OHLCV data for symbol: {}", symbol);

            int updatedCount = ohlcvService.updateOHLCVData(symbol, null, null);

            UpdateResponse response = new UpdateResponse(
                    updatedCount,
                    updatedCount > 0
                        ? String.format("Updated %d data points for %s", updatedCount, symbol)
                        : String.format("No new data available for %s", symbol)
            );

            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update OHLCV data: " + e.getMessage()));
        }
    }

    /**
     * Refresh OHLCV data for all instruments in the database.
     * Processes instruments in descending order by market cap (highest first).
     *
     * @param request Refresh request with configuration options
     * @return Refresh response with processing results
     */
    @PostMapping("/refresh-all")
    @Operation(summary = "Refresh OHLCV data for all instruments with pagination support",
               description = "Refreshes OHLCV data for instruments in the database, processing them in descending order by market cap. " +
                           "Supports pagination through startIndex and endIndex parameters to handle large databases. " +
                           "Can perform actual refresh (dryRun=false) or just validate and report (dryRun=true). " +
                           "Includes rate limiting and error handling for individual symbol failures. " +
                           "Examples: startIndex=0&maxSymbols=1000 (first 1000), startIndex=1000&maxSymbols=1000 (next 1000). " +
                           "CAUTION: When dryRun=false, this will download data from external APIs and may take significant time.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Refresh operation completed successfully",
            content = @Content(schema = @Schema(implementation = RefreshAllResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<RefreshAllResponse>> refreshAllOHLCVData(
            @Parameter(description = "Refresh request with configuration options", required = true)
            @RequestBody RefreshAllRequest request) {

        try {
            // Validate request parameters
            if (request.getMaxSymbols() <= 0) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("maxSymbols must be greater than 0"));
            }

            if (request.getMaxSymbols() > 1000) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("maxSymbols cannot exceed 1000 to prevent system overload"));
            }

            if (request.getStartIndex() < 0) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("startIndex must be >= 0"));
            }

            if (request.getEndIndex() != null && request.getEndIndex() <= request.getStartIndex()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("endIndex must be greater than startIndex"));
            }

            logger.info("Starting OHLCV refresh for all instruments - request: {}", request);

            // Log warning for actual refresh operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time");
            }

            RefreshAllResponse response = ohlcvService.refreshAllOHLCVData(
                    request.isDryRun(), request.getMaxSymbols(), request.isSkipExisting(),
                    request.getStartIndex(), request.getEndIndex());

            String operation = request.isDryRun() ? "OHLCV refresh validation" : "OHLCV refresh";
            logger.info("{} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during OHLCV refresh operation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("OHLCV refresh failed: " + e.getMessage()));
        }
    }

    /**
     * Clean up resources when the bean is destroyed.
     */
    @PreDestroy
    public void cleanup() {
        if (ohlcvService != null) {
            ohlcvService.cleanup();
        }
    }
}
