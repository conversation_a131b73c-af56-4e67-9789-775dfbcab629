package com.investment.provider;

import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import com.investment.model.OHLCV;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

public class YahooFinanceProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceProvider.class);
    // Package-private for testing
    private YahooFinanceScraper scraper = new YahooFinanceScraper();

    // Yahoo Finance’s historical data typically starts from January 2, 1962
    public static final int YAHOO_HISTORICAL_DATA_START_YEAR = 1962;
    // Calculate the difference between current year and Yahoo's earliest data year
    public static final int DEFAULT_HISTORY_YEARS = LocalDate.now().getYear() - YAHOO_HISTORICAL_DATA_START_YEAR - 1;

    @Override
    public void downloadHistoricalData(Instrument instrument, LocalDate startDate, LocalDate endDate, DatabaseManager dbManager) {
        try {
            // Save instrument to database
            dbManager.saveInstrument(instrument.getSymbol(), instrument.getName(), instrument.getType().name());

            // Convert dates to Unix timestamps for Yahoo Finance
            long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);
            long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);

            logger.info("Requesting data from Yahoo Finance for symbol: {}, from {} to {}",
                    instrument.getSymbol(), startDate, endDate);

            // Use the scraper to get historical data
            List<OHLCV> dataPoints = scraper.scrapeHistoricalData(instrument.getSymbol(), period1, period2);

            // Save to database
            dbManager.saveOHLCVData(dataPoints);

            logger.info("Downloaded and saved {} data points for {}", dataPoints.size(), instrument.getSymbol());
        } catch (Exception e) {
            logger.error("Error downloading data for instrument: {}", instrument.getSymbol(), e);
            throw new RuntimeException("Failed to download historical data", e);
        }
    }
}
