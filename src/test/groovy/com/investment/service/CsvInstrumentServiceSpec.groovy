package com.investment.service

import com.investment.database.DatabaseManager
import org.springframework.mock.web.MockMultipartFile
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal

class CsvInstrumentServiceSpec extends Specification {

    DatabaseManager mockDatabaseManager = Mock()

    @Subject
    CsvInstrumentService csvInstrumentService = new CsvInstrumentService(mockDatabaseManager)

    def "should process valid CSV file successfully in dry run mode when symbols exist"() {
        given: "a valid CSV file with symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software
GOOGL,Alphabet Inc.,2500.00,10.00,0.40%,1.7T,United States,2004,1500000,Technology,Internet Services"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL"] // All symbols exist

        when: "processing the CSV file in dry run mode"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should return successful response with correct statistics"
        response.totalRowsInCsv == 3
        response.validRows == 3
        response.invalidRows == 0
        response.processedInstruments == 3
        response.skippedInstruments == 0
        response.addedInstruments == 0
        response.updatedInstruments == 0
        response.validationErrors.isEmpty()
        response.processedSymbols == ["AAPL", "MSFT", "GOOGL"]
        response.dryRun == true
        response.summary.contains("DRY RUN")
        response.summary.contains("3 valid rows")
        response.summary.contains("3 instruments")

        and: "should not call database save methods"
        0 * mockDatabaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should process CSV file and save to database when dry run is false and symbols exist"() {
        given: "a valid CSV file with symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"] // Both symbols exist

        when: "processing the CSV file with dry run false"
        def response = csvInstrumentService.processCsvFile(file, false, 1000, true, true)

        then: "should save instruments to database"
        2 * mockDatabaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)

        and: "should return successful response"
        response.totalRowsInCsv == 2
        response.validRows == 2
        response.processedInstruments == 2
        response.addedInstruments == 0
        response.updatedInstruments == 2
        response.dryRun == false
        response.summary.contains("CSV UPLOAD COMPLETED")
        response.summary.contains("updated 2 existing instruments")
    }

    def "should skip duplicate symbols in CSV when skipDuplicates is true"() {
        given: "a CSV file with duplicate symbols and all symbols exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software
AAPL,Apple Inc. Updated,155.00,1.55,1.03%,2.6T,United States,1980,51000000,Technology,Consumer Electronics"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"] // Both symbols exist

        when: "processing with skipDuplicates enabled"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should skip duplicate symbols in CSV"
        response.totalRowsInCsv == 3
        response.validRows == 3
        response.processedInstruments == 2 // AAPL and MSFT processed once each
        response.skippedInstruments == 1 // Second AAPL skipped
        response.processedSymbols == ["AAPL", "MSFT"]
    }

    def "should handle market cap parsing with different formats"() {
        given: "a CSV file with various market cap formats and symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2200B,United States,1986,30000000,Technology,Software
GOOGL,Alphabet Inc.,2500.00,10.00,0.40%,1700000M,United States,2004,1500000,Technology,Internet Services
TSLA,Tesla Inc.,800.00,5.00,0.63%,800000000000,United States,2010,25000000,Automotive,Electric Vehicles
NVDA,NVIDIA Corporation,400.00,3.00,0.76%,n/a,United States,1999,20000000,Technology,Semiconductors"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"] // All symbols exist

        when: "processing the CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should parse all market cap formats correctly"
        response.totalRowsInCsv == 5
        response.validRows == 5
        response.invalidRows == 0
        response.processedInstruments == 5
        response.validationErrors.isEmpty()
    }

    def "should handle invalid CSV structure"() {
        given: "a CSV file with missing required headers"
        def csvContent = """Symbol,Name,Last Sale
AAPL,Apple Inc.,150.00
MSFT,Microsoft Corporation,300.00"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)

        when: "processing the invalid CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should return validation errors"
        response.totalRowsInCsv == 0
        response.validRows == 0
        response.processedInstruments == 0
        !response.validationErrors.isEmpty()
        response.validationErrors[0].contains("Missing required headers")
    }

    def "should handle empty file"() {
        given: "an empty file"
        def file = new MockMultipartFile("file", "empty.csv", "text/csv", "".bytes)

        when: "processing the empty file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should return validation error"
        response.totalRowsInCsv == 0
        response.validRows == 0
        response.processedInstruments == 0
        !response.validationErrors.isEmpty()
        response.validationErrors[0].contains("empty")
    }

    def "should handle non-CSV file"() {
        given: "a non-CSV file"
        def file = new MockMultipartFile("file", "document.txt", "text/plain", "This is not a CSV file".bytes)

        when: "processing the non-CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should return validation error"
        response.totalRowsInCsv == 0
        response.validRows == 0
        response.processedInstruments == 0
        !response.validationErrors.isEmpty()
        response.validationErrors[0].contains("Invalid file format")
    }

    def "should respect maxInstruments limit"() {
        given: "a CSV file with more instruments than the limit and symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software
GOOGL,Alphabet Inc.,2500.00,10.00,0.40%,1.7T,United States,2004,1500000,Technology,Internet Services
TSLA,Tesla Inc.,800.00,5.00,0.63%,800B,United States,2010,25000000,Automotive,Electric Vehicles
NVDA,NVIDIA Corporation,400.00,3.00,0.76%,1T,United States,1999,20000000,Technology,Semiconductors"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"] // All symbols exist

        when: "processing with a limit of 3 instruments"
        def response = csvInstrumentService.processCsvFile(file, true, 3, true, true)

        then: "should process only up to the limit"
        response.totalRowsInCsv == 4 // All rows are counted
        response.validRows == 3 // Only 3 valid rows processed due to limit
        response.processedInstruments == 3
        response.processedSymbols.size() == 3
    }

    def "should validate required fields when validation is enabled"() {
        given: "a CSV file with missing required fields and symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software
GOOGL,Alphabet Inc.,2500.00,10.00,0.40%,1.7T,United States,2004,1500000,Technology,Internet Services"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL"] // All symbols exist

        when: "processing with validation enabled"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should report validation errors for missing required fields"
        response.totalRowsInCsv == 3
        response.validRows == 1 // Only GOOGL is valid
        response.invalidRows == 2 // AAPL (missing symbol) and MSFT (missing name)
        response.processedInstruments == 1
        response.validationErrors.size() == 2
        response.validationErrors.any { it.contains("Symbol is required") }
        response.validationErrors.any { it.contains("Name is required") }
        response.processedSymbols == ["GOOGL"]
    }

    def "should handle IPO year parsing correctly"() {
        given: "a CSV file with various IPO year formats and symbols that exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,n/a,30000000,Technology,Software
GOOGL,Alphabet Inc.,2500.00,10.00,0.40%,1.7T,United States,,1500000,Technology,Internet Services
TSLA,Tesla Inc.,800.00,5.00,0.63%,800B,United States,2010,25000000,Automotive,Electric Vehicles"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL", "TSLA"] // All symbols exist

        when: "processing the CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should handle all IPO year formats without errors"
        response.totalRowsInCsv == 4
        response.validRows == 4
        response.invalidRows == 0
        response.processedInstruments == 4
        response.validationErrors.isEmpty()
    }

    def "should skip symbols that do not exist in instruments table"() {
        given: "a CSV file with symbols where only some exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
UNKNOWN1,Unknown Company 1,100.00,1.00,1.00%,1B,United States,2020,1000000,Technology,Software
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software
UNKNOWN2,Unknown Company 2,200.00,2.00,1.00%,2B,United States,2021,2000000,Technology,Hardware"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"] // Only AAPL and MSFT exist

        when: "processing the CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should skip non-existent symbols and process only existing ones"
        response.totalRowsInCsv == 4
        response.validRows == 4 // All rows are valid CSV format
        response.invalidRows == 0
        response.processedInstruments == 2 // Only AAPL and MSFT processed
        response.skippedInstruments == 2 // UNKNOWN1 and UNKNOWN2 skipped
        response.addedInstruments == 0
        response.updatedInstruments == 0
        response.validationErrors.isEmpty()
        response.processedSymbols == ["AAPL", "MSFT"]
        response.dryRun == true

        and: "should not call database save methods"
        0 * mockDatabaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should skip all symbols when none exist in instruments table"() {
        given: "a CSV file with symbols that don't exist in database"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
UNKNOWN1,Unknown Company 1,100.00,1.00,1.00%,1B,United States,2020,1000000,Technology,Software
UNKNOWN2,Unknown Company 2,200.00,2.00,1.00%,2B,United States,2021,2000000,Technology,Hardware
UNKNOWN3,Unknown Company 3,300.00,3.00,1.00%,3B,United States,2022,3000000,Technology,Services"""

        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"] // None of the CSV symbols exist

        when: "processing the CSV file"
        def response = csvInstrumentService.processCsvFile(file, true, 1000, true, true)

        then: "should skip all symbols"
        response.totalRowsInCsv == 3
        response.validRows == 3 // All rows are valid CSV format
        response.invalidRows == 0
        response.processedInstruments == 0 // No symbols processed
        response.skippedInstruments == 3 // All symbols skipped
        response.addedInstruments == 0
        response.updatedInstruments == 0
        response.validationErrors.isEmpty()
        response.processedSymbols.isEmpty()
        response.dryRun == true

        and: "should not call database save methods"
        0 * mockDatabaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }
}
