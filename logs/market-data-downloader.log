2025-05-31 00:40:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 00:40:24 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 3880 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 00:40:24 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 00:40:24 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 00:40:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 00:40:24 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 00:40:24 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 00:40:24 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 00:40:24 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 00:40:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 421 ms
2025-05-31 00:40:24 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
2025-05-31 00:40:24 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-05-31 00:40:24 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-31 00:40:24 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-31 00:40:24 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:48)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	... 57 common frames omitted
2025-05-31 00:40:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 00:40:36 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 26704 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 00:40:36 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 00:40:36 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 00:40:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 00:40:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 00:40:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 00:40:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 00:40:36 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 00:40:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 410 ms
2025-05-31 00:40:36 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-31 00:40:36 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-31 00:40:36 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-31 00:40:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 00:40:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-31 00:40:37 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.123 seconds (process running for 1.483)
2025-05-31 00:40:44 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 00:40:44 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 00:40:44 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-05-31 00:40:44 [http-nio-8080-exec-1] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: nasdaq_screener_1748616656955.csv, size: 983826 bytes, dryRun: false, maxInstruments: 15000, skipDuplicates: false, validateData: true
2025-05-31 00:40:44 [http-nio-8080-exec-1] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-31 00:40:44 [http-nio-8080-exec-1] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: nasdaq_screener_1748616656955.csv, dryRun: false, maxInstruments: 15000, skipDuplicates: false, validateData: true
2025-05-31 00:41:03 [http-nio-8080-exec-1] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 6898, Valid: 6898, Invalid: 0, Processed: 6898
2025-05-31 00:41:03 [http-nio-8080-exec-1] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 6898 instruments from 6898 valid rows. Added 6898 new instruments, updated 0 existing instruments, skipped 0 duplicates.
2025-05-31 00:41:23 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 instruments in database, processing up to 100 symbols
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 3, Failed: 0, Skipped: 0
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Found 2 instruments in database, processing up to 100 symbols
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 2500000000000)
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 2025-05-27 to today
2025-05-31 01:18:36 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for MSFT
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: true
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 instruments in database, processing up to 100 symbols
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 1
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 instruments in database, processing up to 2 symbols
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Reached maximum symbols limit: 2
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 instruments in database, processing up to 100 symbols
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: INVALID (market cap: 1000000000)
2025-05-31 01:18:37 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:139)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$610727640.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:174)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:18:37 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:89)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:174)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:139)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$610727640.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	... 107 common frames omitted
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 1800000000000)
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 2025-05-27 to today
2025-05-31 01:18:37 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for GOOGL
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 2, Failed: 1, Skipped: 0
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - Found 0 instruments in database, processing up to 100 symbols
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 0, Successful: 0, Failed: 0, Skipped: 0
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 instruments in database, processing up to 100 symbols
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 4, Successful: 4, Failed: 0, Skipped: 0
2025-05-31 01:18:38 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false
2025-05-31 01:18:38 [Test worker] ERROR com.investment.service.OHLCVService - Error during OHLCV refresh operation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_7_closure2.doCall(OHLCVServiceSpec.groovy:198)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$610727640.getAllInstrumentsOrderedByMarketCap(Unknown Source)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:142)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_7(OHLCVServiceSpec.groovy:201)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: AAPL, from 2025-05-21 to 2025-05-31
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: UNKNOWN, from 2025-05-21 to 2025-05-31
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for 3 symbols
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for symbol: AAPL
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false}
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 10 out of 50 instruments. Skipped 5 symbols with recent data. Estimated 10 successful updates, 0 failures.
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=50, skipExisting=true}
2025-05-31 01:19:04 [Test worker] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 01:19:04 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 25 out of 100 instruments. Successfully updated 20 symbols (150 data points), failed 5 symbols, skipped 10 symbols with recent data.
2025-05-31 01:19:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false}
2025-05-31 01:19:05 [Test worker] ERROR c.i.api.controller.OHLCVController - Error during OHLCV refresh operation
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.OHLCVControllerSpec$__spock_feature_0_8_closure1.doCall(OHLCVControllerSpec.groovy:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.OHLCVService$SpockMock$754653717.refreshAllOHLCVData(Unknown Source)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:230)
	at com.investment.api.controller.OHLCVController$refreshAllOHLCVData$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.OHLCVControllerSpec.$spock_feature_0_8(OHLCVControllerSpec.groovy:177)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:19:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false}
2025-05-31 01:19:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 10 instruments. Skipped 2 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-05-31 01:19:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=50, skipExisting=true}
2025-05-31 01:19:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 20 instruments. Skipped 10 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 3, Failed: 0, Skipped: 0
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Found 2 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:38:34 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 2500000000000)
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 2025-05-27 to today
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for MSFT
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 1
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:38:35 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:38:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: INVALID (market cap: 1000000000)
2025-05-31 01:38:36 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$796570684.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:38:36 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:89)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$796570684.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	... 108 common frames omitted
2025-05-31 01:38:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 1800000000000)
2025-05-31 01:38:36 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 2025-05-27 to today
2025-05-31 01:38:36 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for GOOGL
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 2, Failed: 1, Skipped: 0
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 0 total instruments in database, fetching 0 instruments from index 0 to -1
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 0, Successful: 0, Failed: 0, Skipped: 0
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 4 instruments from index 0 to 3
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 4, Successful: 4, Failed: 0, Skipped: 0
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:38:37 [Test worker] ERROR com.investment.service.OHLCVService - Error during OHLCV refresh operation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_7_closure2.doCall(OHLCVServiceSpec.groovy:205)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$796570684.getTotalInstrumentCount(Unknown Source)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:162)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_7(OHLCVServiceSpec.groovy:208)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 1, endIndex: 3
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 1 to 2
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 5, endIndex: 10
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - Found 10 total instruments in database, fetching 2 instruments from index 5 to 6
2025-05-31 01:38:37 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 3, Failed: 0, Skipped: 0
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Found 2 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:45:42 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 2500000000000)
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 2025-05-27 to today
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for MSFT
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 1
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:45:43 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: INVALID (market cap: 1000000000)
2025-05-31 01:45:44 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1121251494.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:45:44 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:89)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1121251494.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	... 108 common frames omitted
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 1800000000000)
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 2025-05-27 to today
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for GOOGL
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 2, Failed: 1, Skipped: 0
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Found 0 total instruments in database, fetching 0 instruments from index 0 to -1
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 0, Successful: 0, Failed: 0, Skipped: 0
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 4 instruments from index 0 to 3
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 4, Successful: 4, Failed: 0, Skipped: 0
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:45:44 [Test worker] ERROR com.investment.service.OHLCVService - Error during OHLCV refresh operation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_7_closure2.doCall(OHLCVServiceSpec.groovy:205)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1121251494.getTotalInstrumentCount(Unknown Source)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:162)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_7(OHLCVServiceSpec.groovy:208)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 1, endIndex: 3
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 1 to 2
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:45:44 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 5, endIndex: 10
2025-05-31 01:45:44 [Test worker] ERROR com.investment.service.OHLCVService - Error during OHLCV refresh operation
java.lang.NullPointerException: Cannot invoke "java.util.List.size()" because "instruments" is null
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:168)
	at com.investment.service.OHLCVService$refreshAllOHLCVData$0.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_9(OHLCVServiceSpec.groovy:252)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 3, Failed: 0, Skipped: 0
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Found 2 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 2500000000000)
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 2025-05-27 to today
2025-05-31 01:46:16 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for MSFT
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 1
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 0 to 1
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Found 3 total instruments in database, fetching 3 instruments from index 0 to 2
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 3000000000000)
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 2025-05-22 to today
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 10 data points for AAPL
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: INVALID (market cap: 1000000000)
2025-05-31 01:46:17 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1807656609.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:46:17 [Test worker] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: INVALID
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:89)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_4(OHLCVServiceSpec.groovy:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.RuntimeException: Symbol not found
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_4_closure1.doCall(OHLCVServiceSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1807656609.getLastDataDate(Unknown Source)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:61)
	... 108 common frames omitted
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 1800000000000)
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 2025-05-27 to today
2025-05-31 01:46:17 [Test worker] INFO  com.investment.service.OHLCVService - Successfully updated 5 data points for GOOGL
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 3, Successful: 2, Failed: 1, Skipped: 0
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Found 0 total instruments in database, fetching 0 instruments from index 0 to -1
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 0, Successful: 0, Failed: 0, Skipped: 0
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 4 instruments from index 0 to 3
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 4, Successful: 4, Failed: 0, Skipped: 0
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 0, endIndex: null
2025-05-31 01:46:18 [Test worker] ERROR com.investment.service.OHLCVService - Error during OHLCV refresh operation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.OHLCVServiceSpec$__spock_feature_0_7_closure2.doCall(OHLCVServiceSpec.groovy:205)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1807656609.getTotalInstrumentCount(Unknown Source)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:162)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:127)
	at com.investment.service.OHLCVService$refreshAllOHLCVData.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:157)
	at com.investment.service.OHLCVServiceSpec.$spock_feature_0_7(OHLCVServiceSpec.groovy:208)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 100, skipExisting: false, startIndex: 1, endIndex: 3
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Found 4 total instruments in database, fetching 2 instruments from index 1 to 2
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: true, maxSymbols: 2, skipExisting: false, startIndex: 5, endIndex: 10
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - Found 10 total instruments in database, fetching 2 instruments from index 5 to 6
2025-05-31 01:46:18 [Test worker] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 2, Successful: 2, Failed: 0, Skipped: 0
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: AAPL, from 2025-05-21 to 2025-05-31
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: UNKNOWN, from 2025-05-21 to 2025-05-31
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for 3 symbols
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for symbol: AAPL
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 10 out of 50 instruments (range: 0-99). Skipped 5 symbols with recent data. Estimated 10 successful updates, 0 failures.
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=50, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 01:47:05 [Test worker] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 25 out of 100 instruments (range: 0-49). Successfully updated 20 symbols (150 data points), failed 5 symbols, skipped 10 symbols with recent data.
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-05-31 01:47:05 [Test worker] ERROR c.i.api.controller.OHLCVController - Error during OHLCV refresh operation
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.OHLCVControllerSpec$__spock_feature_0_8_closure1.doCall(OHLCVControllerSpec.groovy:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.OHLCVService$SpockMock$290513497.refreshAllOHLCVData(Unknown Source)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at com.investment.api.controller.OHLCVController$refreshAllOHLCVData$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.OHLCVControllerSpec.$spock_feature_0_8(OHLCVControllerSpec.groovy:177)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 10 instruments (range: 0-99). Skipped 2 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=50, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 20 instruments (range: 0-49). Skipped 10 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=1000, endIndex=1500}
2025-05-31 01:47:05 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 500 out of 5000 instruments (range: 1000-1499). Skipped 50 symbols with recent data. Estimated 450 successful updates, 0 failures.
2025-05-31 01:59:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 01:59:39 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 36844 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 01:59:39 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 01:59:39 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 01:59:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 01:59:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 01:59:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 01:59:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 01:59:40 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 01:59:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 434 ms
2025-05-31 01:59:40 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
2025-05-31 01:59:40 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-05-31 01:59:40 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-31 01:59:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-31 01:59:40 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:48)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	... 57 common frames omitted
2025-05-31 01:59:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 01:59:59 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 38428 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 01:59:59 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 01:59:59 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 02:00:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 02:00:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 02:00:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 02:00:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 02:00:00 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 02:00:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 446 ms
2025-05-31 02:00:00 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-31 02:00:00 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-31 02:00:00 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-31 02:00:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 02:00:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-31 02:00:00 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.212 seconds (process running for 1.566)
2025-05-31 02:00:17 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 02:00:17 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 02:00:17 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-05-31 02:00:17 [http-nio-8080-exec-1] ERROR c.i.a.e.GlobalExceptionHandler - Unhandled exception
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character (''' (code 39)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:406)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:354)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:184)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:159)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:134)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:228)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.fasterxml.jackson.core.JsonParseException: Unexpected character (''' (code 39)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 2]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2477)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:750)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportUnexpectedChar(ParserMinimalBase.java:674)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._handleUnexpectedValue(UTF8StreamJsonParser.java:2790)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._nextTokenNotInObject(UTF8StreamJsonParser.java:908)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:794)
	at com.fasterxml.jackson.databind.ObjectReader._initForReading(ObjectReader.java:357)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2095)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1481)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:395)
	... 51 common frames omitted
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=10, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 02:01:24 [http-nio-8080-exec-2] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 10, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Found 10415 total instruments in database, fetching 10 instruments from index 0 to 9
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 3412801166492.00)
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 2020-05-31 to today
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: MSFT, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:24 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/MSFT/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:26 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MSFT_1748628086.html
2025-05-31 02:01:26 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MSFT_1748628086.html
2025-05-31 02:01:26 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for MSFT
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for MSFT
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for MSFT
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for MSFT
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: NVDA (market cap: 3349754000000.00)
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for NVDA from 2020-05-31 to today
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: NVDA, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:27 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/NVDA/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:28 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVDA_1748628088.html
2025-05-31 02:01:28 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVDA_1748628088.html
2025-05-31 02:01:28 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for NVDA
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for NVDA
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for NVDA
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for NVDA
2025-05-31 02:01:29 [http-nio-8080-exec-2] DEBUG com.investment.service.OHLCVService - Skipping symbol AAPL - has recent data
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AMZN (market cap: 2175577249709.00)
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for AMZN from 2020-05-31 to today
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AMZN, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:29 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AMZN/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AMZN_1748628091.html
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AMZN_1748628091.html
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for AMZN
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for AMZN
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for AMZN
2025-05-31 02:01:31 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for AMZN
2025-05-31 02:01:32 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOG (market cap: 2085399750000.00)
2025-05-31 02:01:32 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for GOOG from 2020-05-31 to today
2025-05-31 02:01:32 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: GOOG, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:32 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/GOOG/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOG_1748628093.html
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOG_1748628093.html
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for GOOG
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for GOOG
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for GOOG
2025-05-31 02:01:33 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for GOOG
2025-05-31 02:01:34 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 2071565850000.00)
2025-05-31 02:01:34 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 2020-05-31 to today
2025-05-31 02:01:34 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: GOOGL, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:34 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/GOOGL/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOGL_1748628095.html
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOGL_1748628095.html
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for GOOGL
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for GOOGL
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for GOOGL
2025-05-31 02:01:35 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for GOOGL
2025-05-31 02:01:36 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: META (market cap: 1622093081420.00)
2025-05-31 02:01:36 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for META from 2020-05-31 to today
2025-05-31 02:01:36 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: META, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:36 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/META/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:39 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_META_1748628099.html
2025-05-31 02:01:39 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_META_1748628099.html
2025-05-31 02:01:39 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for META
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for META
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for META
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for META
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: TSLA (market cap: 1163731479034.00)
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for TSLA from 2020-05-31 to today
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: TSLA, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:40 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/TSLA/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSLA_1748628103.html
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSLA_1748628103.html
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for TSLA
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for TSLA
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for TSLA
2025-05-31 02:01:43 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for TSLA
2025-05-31 02:01:44 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AVGO (market cap: 1134791813104.00)
2025-05-31 02:01:44 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for AVGO from 2020-05-31 to today
2025-05-31 02:01:44 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AVGO, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:44 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AVGO/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AVGO_1748628105.html
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AVGO_1748628105.html
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Extracted 1257 OHLCV data points for AVGO
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.database.DatabaseManager - Saved 1257 OHLCV data points for AVGO
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1257 data points for AVGO
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for AVGO
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: BRK/A (market cap: 1111980744000.00)
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Updating data for BRK/A from 2020-05-31 to today
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: BRK/A, from 2020-05-31 to 2025-05-31
2025-05-31 02:01:45 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/BRK/A/history/?period1=**********&period2=**********&interval=1d
2025-05-31 02:01:46 [http-nio-8080-exec-2] ERROR c.i.provider.YahooFinanceScraper - Failed to save HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748628106.html
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748628106.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:32)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:79)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:01:46 [http-nio-8080-exec-2] WARN  c.i.provider.YahooFinanceScraper - Failed to save HTML content to file, continuing with in-memory content
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748628106.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:32)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:79)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:196)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:01:46 [http-nio-8080-exec-2] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for BRK/A
2025-05-31 02:01:46 [http-nio-8080-exec-2] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for BRK/A
2025-05-31 02:01:46 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - Successfully updated 1827 data points for BRK/A
2025-05-31 02:01:47 [http-nio-8080-exec-2] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 9, Successful: 9, Failed: 0, Skipped: 1
2025-05-31 02:01:47 [http-nio-8080-exec-2] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 9 out of 10415 instruments (range: 0-9). Successfully updated 9 symbols (16443 data points), failed 0 symbols, skipped 1 symbols with recent data.
2025-05-31 02:05:33 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-31 02:09:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 02:09:15 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 42432 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 02:09:15 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 02:09:15 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 02:09:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 02:09:15 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 02:09:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 02:09:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 02:09:15 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 02:09:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 476 ms
2025-05-31 02:09:15 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-31 02:09:15 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-31 02:09:15 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-31 02:09:16 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 02:09:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-31 02:09:16 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.235 seconds (process running for 1.666)
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=10, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 02:09:23 [http-nio-8080-exec-1] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 10, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Found 10415 total instruments in database, fetching 10 instruments from index 0 to 9
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AAPL (market cap: 2974171031380.00)
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for AAPL from 1962-01-01 to today
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPL, from 1962-01-01 to 2025-05-31
2025-05-31 02:09:23 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPL/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:09:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748628568.html
2025-05-31 02:09:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748628568.html
2025-05-31 02:09:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 11206 OHLCV data points for AAPL
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 11206 OHLCV data points for AAPL
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11206 data points for AAPL
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for AAPL
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: BRK/B (market cap: 1111760299847.00)
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for BRK/B from 1962-01-01 to today
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: BRK/B, from 1962-01-01 to 2025-05-31
2025-05-31 02:09:32 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/BRK/B/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:09:33 [http-nio-8080-exec-1] ERROR c.i.provider.YahooFinanceScraper - Failed to save HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\B_1748628573.html
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\B_1748628573.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:09:33 [http-nio-8080-exec-1] WARN  c.i.provider.YahooFinanceScraper - Failed to save HTML content to file, continuing with in-memory content
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\B_1748628573.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:09:33 [http-nio-8080-exec-1] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for BRK/B
2025-05-31 02:09:33 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for BRK/B
2025-05-31 02:09:33 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for BRK/B
2025-05-31 02:09:34 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: TSM (market cap: 1010054026965.00)
2025-05-31 02:09:34 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for TSM from 1962-01-01 to today
2025-05-31 02:09:34 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: TSM, from 1962-01-01 to 2025-05-31
2025-05-31 02:09:34 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/TSM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:09:40 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSM_1748628580.html
2025-05-31 02:09:40 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSM_1748628580.html
2025-05-31 02:09:40 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 6953 OHLCV data points for TSM
2025-05-31 02:09:42 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 6953 OHLCV data points for TSM
2025-05-31 02:09:42 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6953 data points for TSM
2025-05-31 02:09:42 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for TSM
2025-05-31 02:09:43 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: WMT (market cap: 785170234545.00)
2025-05-31 02:09:43 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for WMT from 1962-01-01 to today
2025-05-31 02:09:43 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: WMT, from 1962-01-01 to 2025-05-31
2025-05-31 02:09:43 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/WMT/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:09:49 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_WMT_1748628589.html
2025-05-31 02:09:49 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_WMT_1748628589.html
2025-05-31 02:09:49 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 13248 OHLCV data points for WMT
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 13248 OHLCV data points for WMT
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 13248 data points for WMT
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for WMT
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: JPM (market cap: 734681418848.00)
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for JPM from 1962-01-01 to today
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: JPM, from 1962-01-01 to 2025-05-31
2025-05-31 02:09:53 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/JPM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:00 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_JPM_1748628600.html
2025-05-31 02:10:00 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_JPM_1748628600.html
2025-05-31 02:10:00 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 11395 OHLCV data points for JPM
2025-05-31 02:10:03 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 11395 OHLCV data points for JPM
2025-05-31 02:10:03 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11395 data points for JPM
2025-05-31 02:10:03 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for JPM
2025-05-31 02:10:04 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: LLY (market cap: 697533344362.00)
2025-05-31 02:10:04 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for LLY from 1962-01-01 to today
2025-05-31 02:10:04 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: LLY, from 1962-01-01 to 2025-05-31
2025-05-31 02:10:04 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/LLY/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:14 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_LLY_1748628614.html
2025-05-31 02:10:14 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_LLY_1748628614.html
2025-05-31 02:10:14 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 13360 OHLCV data points for LLY
2025-05-31 02:10:18 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 13360 OHLCV data points for LLY
2025-05-31 02:10:18 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 13360 data points for LLY
2025-05-31 02:10:18 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for LLY
2025-05-31 02:10:19 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: V (market cap: 695879650559.00)
2025-05-31 02:10:19 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for V from 1962-01-01 to today
2025-05-31 02:10:19 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: V, from 1962-01-01 to 2025-05-31
2025-05-31 02:10:19 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/V/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:21 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_V_1748628621.html
2025-05-31 02:10:21 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_V_1748628621.html
2025-05-31 02:10:21 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 4328 OHLCV data points for V
2025-05-31 02:10:22 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 4328 OHLCV data points for V
2025-05-31 02:10:22 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4328 data points for V
2025-05-31 02:10:22 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for V
2025-05-31 02:10:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MA (market cap: 526834702103.00)
2025-05-31 02:10:23 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for MA from 1962-01-01 to today
2025-05-31 02:10:23 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: MA, from 1962-01-01 to 2025-05-31
2025-05-31 02:10:23 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/MA/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:25 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MA_1748628625.html
2025-05-31 02:10:25 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MA_1748628625.html
2025-05-31 02:10:26 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 4784 OHLCV data points for MA
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 4784 OHLCV data points for MA
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4784 data points for MA
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for MA
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: NFLX (market cap: 506750107671.00)
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for NFLX from 1962-01-01 to today
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: NFLX, from 1962-01-01 to 2025-05-31
2025-05-31 02:10:27 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/NFLX/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:30 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NFLX_1748628630.html
2025-05-31 02:10:30 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NFLX_1748628630.html
2025-05-31 02:10:31 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 5793 OHLCV data points for NFLX
2025-05-31 02:10:32 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 5793 OHLCV data points for NFLX
2025-05-31 02:10:32 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5793 data points for NFLX
2025-05-31 02:10:32 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for NFLX
2025-05-31 02:10:33 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: COST (market cap: 460742982077.00)
2025-05-31 02:10:33 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for COST from 1962-01-01 to today
2025-05-31 02:10:33 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: COST, from 1962-01-01 to 2025-05-31
2025-05-31 02:10:33 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/COST/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:10:37 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_COST_1748628637.html
2025-05-31 02:10:37 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_COST_1748628637.html
2025-05-31 02:10:38 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 9800 OHLCV data points for COST
2025-05-31 02:10:40 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 9800 OHLCV data points for COST
2025-05-31 02:10:40 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9800 data points for COST
2025-05-31 02:10:40 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for COST
2025-05-31 02:10:41 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 10, Successful: 10, Failed: 0, Skipped: 0
2025-05-31 02:10:41 [http-nio-8080-exec-1] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 10 out of 10415 instruments (range: 0-9). Successfully updated 10 symbols (231620 data points), failed 0 symbols, skipped 0 symbols with recent data.
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=10, skipExisting=true, startIndex=10, endIndex=null}
2025-05-31 02:30:59 [http-nio-8080-exec-3] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 10, skipExisting: true, startIndex: 10, endIndex: null
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Found 10415 total instruments in database, fetching 10 instruments from index 10 to 19
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: SMFG (market cap: 302331211947.00)
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for SMFG from 1962-01-01 to today
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: SMFG, from 1962-01-01 to 2025-05-31
2025-05-31 02:30:59 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/SMFG/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:03 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_SMFG_1748629863.html
2025-05-31 02:31:03 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_SMFG_1748629863.html
2025-05-31 02:31:03 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 4864 OHLCV data points for SMFG
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 4864 OHLCV data points for SMFG
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4864 data points for SMFG
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for SMFG
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: PLTR (market cap: 302068786816.00)
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for PLTR from 1962-01-01 to today
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: PLTR, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/PLTR/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_PLTR_1748629867.html
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_PLTR_1748629867.html
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 1172 OHLCV data points for PLTR
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 1172 OHLCV data points for PLTR
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1172 data points for PLTR
2025-05-31 02:31:07 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for PLTR
2025-05-31 02:31:08 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: ASML (market cap: 292721072796.00)
2025-05-31 02:31:08 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for ASML from 1962-01-01 to today
2025-05-31 02:31:08 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ASML, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:08 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ASML/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:15 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ASML_1748629875.html
2025-05-31 02:31:15 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ASML_1748629875.html
2025-05-31 02:31:15 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 7604 OHLCV data points for ASML
2025-05-31 02:31:17 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 7604 OHLCV data points for ASML
2025-05-31 02:31:17 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7604 data points for ASML
2025-05-31 02:31:17 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for ASML
2025-05-31 02:31:18 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: PM (market cap: 279363703825.00)
2025-05-31 02:31:18 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for PM from 1962-01-01 to today
2025-05-31 02:31:18 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: PM, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:18 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/PM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:21 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_PM_1748629881.html
2025-05-31 02:31:21 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_PM_1748629881.html
2025-05-31 02:31:21 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 4330 OHLCV data points for PM
2025-05-31 02:31:22 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 4330 OHLCV data points for PM
2025-05-31 02:31:22 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4330 data points for PM
2025-05-31 02:31:22 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for PM
2025-05-31 02:31:23 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: UNH (market cap: 274954267667.00)
2025-05-31 02:31:23 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for UNH from 1962-01-01 to today
2025-05-31 02:31:23 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: UNH, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:23 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/UNH/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:43 [http-nio-8080-exec-3] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: UNH
okhttp3.internal.http2.StreamResetException: stream was reset: NO_ERROR
	at okhttp3.internal.http2.Http2Stream$FramingSource.read(Http2Stream.kt:355)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:276)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:189)
	at okio.RealBufferedSource.exhausted(RealBufferedSource.kt:197)
	at okio.InflaterSource.refill(InflaterSource.kt:112)
	at okio.InflaterSource.readOrInflate(InflaterSource.kt:76)
	at okio.InflaterSource.read(InflaterSource.kt:49)
	at okio.GzipSource.read(GzipSource.kt:69)
	at okio.Buffer.writeAll(Buffer.kt:1290)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:95)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:153)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:31:43 [http-nio-8080-exec-3] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: UNH
java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: okhttp3.internal.http2.StreamResetException: stream was reset: NO_ERROR
	at okhttp3.internal.http2.Http2Stream$FramingSource.read(Http2Stream.kt:355)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:276)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:189)
	at okio.RealBufferedSource.exhausted(RealBufferedSource.kt:197)
	at okio.InflaterSource.refill(InflaterSource.kt:112)
	at okio.InflaterSource.readOrInflate(InflaterSource.kt:76)
	at okio.InflaterSource.read(InflaterSource.kt:49)
	at okio.GzipSource.read(GzipSource.kt:69)
	at okio.Buffer.writeAll(Buffer.kt:1290)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:95)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:153)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:31:43 [http-nio-8080-exec-3] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: UNH
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:90)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	... 50 common frames omitted
Caused by: okhttp3.internal.http2.StreamResetException: stream was reset: NO_ERROR
	at okhttp3.internal.http2.Http2Stream$FramingSource.read(Http2Stream.kt:355)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:276)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:189)
	at okio.RealBufferedSource.exhausted(RealBufferedSource.kt:197)
	at okio.InflaterSource.refill(InflaterSource.kt:112)
	at okio.InflaterSource.readOrInflate(InflaterSource.kt:76)
	at okio.InflaterSource.read(InflaterSource.kt:49)
	at okio.GzipSource.read(GzipSource.kt:69)
	at okio.Buffer.writeAll(Buffer.kt:1290)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:95)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:153)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:31:43 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: TMUS (market cap: 274561981565.00)
2025-05-31 02:31:43 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for TMUS from 1962-01-01 to today
2025-05-31 02:31:43 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: TMUS, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:43 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/TMUS/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:46 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TMUS_1748629906.html
2025-05-31 02:31:46 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TMUS_1748629906.html
2025-05-31 02:31:46 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 4559 OHLCV data points for TMUS
2025-05-31 02:31:47 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 4559 OHLCV data points for TMUS
2025-05-31 02:31:47 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4559 data points for TMUS
2025-05-31 02:31:47 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for TMUS
2025-05-31 02:31:48 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: BABA (market cap: 271765266758.00)
2025-05-31 02:31:48 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for BABA from 1962-01-01 to today
2025-05-31 02:31:48 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: BABA, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:48 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/BABA/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:31:49 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BABA_1748629909.html
2025-05-31 02:31:50 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BABA_1748629909.html
2025-05-31 02:31:50 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 2690 OHLCV data points for BABA
2025-05-31 02:31:50 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 2690 OHLCV data points for BABA
2025-05-31 02:31:50 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2690 data points for BABA
2025-05-31 02:31:50 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for BABA
2025-05-31 02:31:51 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GE (market cap: 260731534214.00)
2025-05-31 02:31:51 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for GE from 1962-01-01 to today
2025-05-31 02:31:51 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: GE, from 1962-01-01 to 2025-05-31
2025-05-31 02:31:51 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/GE/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:32:00 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GE_1748629920.html
2025-05-31 02:32:00 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GE_1748629920.html
2025-05-31 02:32:00 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 15959 OHLCV data points for GE
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 15959 OHLCV data points for GE
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15959 data points for GE
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for GE
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: TM (market cap: 257666591730.00)
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for TM from 1962-01-01 to today
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: TM, from 1962-01-01 to 2025-05-31
2025-05-31 02:32:05 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/TM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:32:12 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TM_1748629932.html
2025-05-31 02:32:12 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TM_1748629932.html
2025-05-31 02:32:12 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 11262 OHLCV data points for TM
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 11262 OHLCV data points for TM
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11262 data points for TM
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for TM
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: CRM (market cap: 254189305000.00)
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Updating data for CRM from 1962-01-01 to today
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: CRM, from 1962-01-01 to 2025-05-31
2025-05-31 02:32:16 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/CRM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:32:19 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CRM_1748629939.html
2025-05-31 02:32:19 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CRM_1748629939.html
2025-05-31 02:32:19 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceScraper - Extracted 5269 OHLCV data points for CRM
2025-05-31 02:32:21 [http-nio-8080-exec-3] INFO  c.i.database.DatabaseManager - Saved 5269 OHLCV data points for CRM
2025-05-31 02:32:21 [http-nio-8080-exec-3] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5269 data points for CRM
2025-05-31 02:32:21 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for CRM
2025-05-31 02:32:21 [http-nio-8080-exec-3] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 10, Successful: 9, Failed: 1, Skipped: 0
2025-05-31 02:32:21 [http-nio-8080-exec-3] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 10 out of 10415 instruments (range: 10-19). Successfully updated 9 symbols (208458 data points), failed 1 symbols, skipped 0 symbols with recent data.
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=20, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 02:33:07 [http-nio-8080-exec-5] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 20, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Found 10415 total instruments in database, fetching 20 instruments from index 0 to 19
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: ORCL (market cap: 458239877940.00)
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for ORCL from 1962-01-01 to today
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ORCL, from 1962-01-01 to 2025-05-31
2025-05-31 02:33:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ORCL/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:33:12 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ORCL_1748629992.html
2025-05-31 02:33:12 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ORCL_1748629992.html
2025-05-31 02:33:13 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 9882 OHLCV data points for ORCL
2025-05-31 02:33:15 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 9882 OHLCV data points for ORCL
2025-05-31 02:33:15 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9882 data points for ORCL
2025-05-31 02:33:15 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for ORCL
2025-05-31 02:33:16 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: XOM (market cap: 439583159742.00)
2025-05-31 02:33:16 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for XOM from 1962-01-01 to today
2025-05-31 02:33:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: XOM, from 1962-01-01 to 2025-05-31
2025-05-31 02:33:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/XOM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:33:25 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_XOM_1748630005.html
2025-05-31 02:33:25 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_XOM_1748630005.html
2025-05-31 02:33:25 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 15960 OHLCV data points for XOM
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 15960 OHLCV data points for XOM
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15960 data points for XOM
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for XOM
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: PG (market cap: 396450335239.00)
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for PG from 1962-01-01 to today
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: PG, from 1962-01-01 to 2025-05-31
2025-05-31 02:33:30 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/PG/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:33:41 [http-nio-8080-exec-5] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: PG
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/PG/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:33:41 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: PG
java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/PG/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:33:41 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: PG
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:90)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	... 50 common frames omitted
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/PG/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:33:41 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: SAP (market cap: 371855945984.00)
2025-05-31 02:33:41 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for SAP from 1962-01-01 to today
2025-05-31 02:33:41 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: SAP, from 1962-01-01 to 2025-05-31
2025-05-31 02:33:41 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/SAP/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:33:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_SAP_1748630027.html
2025-05-31 02:33:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_SAP_1748630027.html
2025-05-31 02:33:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 7460 OHLCV data points for SAP
2025-05-31 02:33:49 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 7460 OHLCV data points for SAP
2025-05-31 02:33:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7460 data points for SAP
2025-05-31 02:33:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for SAP
2025-05-31 02:33:50 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: JNJ (market cap: 371305228415.00)
2025-05-31 02:33:50 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for JNJ from 1962-01-01 to today
2025-05-31 02:33:50 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: JNJ, from 1962-01-01 to 2025-05-31
2025-05-31 02:33:50 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/JNJ/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:00 [http-nio-8080-exec-5] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: JNJ
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/JNJ/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:34:00 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: JNJ
java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/JNJ/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:34:00 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: JNJ
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:90)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	... 50 common frames omitted
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/JNJ/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:34:00 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: HD (market cap: 367312488142.00)
2025-05-31 02:34:00 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for HD from 1962-01-01 to today
2025-05-31 02:34:00 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: HD, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:00 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/HD/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:05 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_HD_1748630045.html
2025-05-31 02:34:05 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_HD_1748630045.html
2025-05-31 02:34:05 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 11012 OHLCV data points for HD
2025-05-31 02:34:08 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 11012 OHLCV data points for HD
2025-05-31 02:34:08 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11012 data points for HD
2025-05-31 02:34:08 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for HD
2025-05-31 02:34:09 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: BAC (market cap: 332193395523.00)
2025-05-31 02:34:09 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for BAC from 1962-01-01 to today
2025-05-31 02:34:09 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: BAC, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:09 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/BAC/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BAC_1748630056.html
2025-05-31 02:34:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BAC_1748630056.html
2025-05-31 02:34:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 13175 OHLCV data points for BAC
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 13175 OHLCV data points for BAC
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 13175 data points for BAC
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for BAC
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: ABBV (market cap: 327155504631.00)
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for ABBV from 1962-01-01 to today
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABBV, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:20 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABBV/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:23 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBV_1748630063.html
2025-05-31 02:34:23 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBV_1748630063.html
2025-05-31 02:34:23 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 3122 OHLCV data points for ABBV
2025-05-31 02:34:24 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 3122 OHLCV data points for ABBV
2025-05-31 02:34:24 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3122 data points for ABBV
2025-05-31 02:34:24 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for ABBV
2025-05-31 02:34:25 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: NVO (market cap: 312505350000.00)
2025-05-31 02:34:25 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for NVO from 1962-01-01 to today
2025-05-31 02:34:25 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: NVO, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:25 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/NVO/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:34 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVO_1748630074.html
2025-05-31 02:34:34 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVO_1748630074.html
2025-05-31 02:34:35 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 11047 OHLCV data points for NVO
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 11047 OHLCV data points for NVO
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11047 data points for NVO
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for NVO
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: KO (market cap: 309369171794.00)
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for KO from 1962-01-01 to today
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: KO, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:38 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/KO/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:49 [http-nio-8080-exec-5] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: KO
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/KO/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:34:49 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: KO
java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/KO/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:34:49 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: KO
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:90)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	... 50 common frames omitted
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/KO/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:34:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: CSCO (market cap: 249064085531.00)
2025-05-31 02:34:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for CSCO from 1962-01-01 to today
2025-05-31 02:34:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: CSCO, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/CSCO/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:34:53 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CSCO_1748630093.html
2025-05-31 02:34:53 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CSCO_1748630093.html
2025-05-31 02:34:53 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 8886 OHLCV data points for CSCO
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 8886 OHLCV data points for CSCO
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8886 data points for CSCO
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for CSCO
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: WFC (market cap: 242355237666.00)
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for WFC from 1962-01-01 to today
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: WFC, from 1962-01-01 to 2025-05-31
2025-05-31 02:34:56 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/WFC/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:02 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_WFC_1748630102.html
2025-05-31 02:35:03 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_WFC_1748630102.html
2025-05-31 02:35:03 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 13354 OHLCV data points for WFC
2025-05-31 02:35:06 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 13354 OHLCV data points for WFC
2025-05-31 02:35:06 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 13354 data points for WFC
2025-05-31 02:35:06 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for WFC
2025-05-31 02:35:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: IBM (market cap: 240341954295.00)
2025-05-31 02:35:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for IBM from 1962-01-01 to today
2025-05-31 02:35:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: IBM, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/IBM/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:15 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_IBM_1748630115.html
2025-05-31 02:35:15 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_IBM_1748630115.html
2025-05-31 02:35:16 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 15957 OHLCV data points for IBM
2025-05-31 02:35:20 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 15957 OHLCV data points for IBM
2025-05-31 02:35:20 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15957 data points for IBM
2025-05-31 02:35:20 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for IBM
2025-05-31 02:35:21 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: CVX (market cap: 238749510753.00)
2025-05-31 02:35:21 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for CVX from 1962-01-01 to today
2025-05-31 02:35:21 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: CVX, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:21 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/CVX/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:29 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CVX_1748630129.html
2025-05-31 02:35:29 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_CVX_1748630129.html
2025-05-31 02:35:29 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 15960 OHLCV data points for CVX
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 15960 OHLCV data points for CVX
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15960 data points for CVX
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for CVX
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: ABT (market cap: 231198168652.00)
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for ABT from 1962-01-01 to today
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABT, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:34 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABT/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:40 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABT_1748630140.html
2025-05-31 02:35:40 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABT_1748630140.html
2025-05-31 02:35:40 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 11395 OHLCV data points for ABT
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 11395 OHLCV data points for ABT
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11395 data points for ABT
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for ABT
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: NVS (market cap: 227866046542.00)
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for NVS from 1962-01-01 to today
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: NVS, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:43 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/NVS/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVS_1748630147.html
2025-05-31 02:35:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVS_1748630147.html
2025-05-31 02:35:47 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 7172 OHLCV data points for NVS
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 7172 OHLCV data points for NVS
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7172 data points for NVS
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for NVS
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MCD (market cap: 223941099769.00)
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for MCD from 1962-01-01 to today
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: MCD, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:49 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/MCD/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:35:59 [http-nio-8080-exec-5] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: MCD
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/MCD/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 02:35:59 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: MCD
java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/MCD/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:35:59 [http-nio-8080-exec-5] ERROR com.investment.service.OHLCVService - Error updating OHLCV data for symbol: MCD
java.lang.RuntimeException: Failed to update OHLCV data
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:90)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to download historical data
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:45)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	... 50 common frames omitted
Caused by: java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/MCD/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	... 51 common frames omitted
2025-05-31 02:35:59 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AZN (market cap: 221493196484.00)
2025-05-31 02:35:59 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for AZN from 1962-01-01 to today
2025-05-31 02:35:59 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AZN, from 1962-01-01 to 2025-05-31
2025-05-31 02:35:59 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AZN/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:36:04 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AZN_1748630164.html
2025-05-31 02:36:04 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AZN_1748630164.html
2025-05-31 02:36:04 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 8069 OHLCV data points for AZN
2025-05-31 02:36:06 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 8069 OHLCV data points for AZN
2025-05-31 02:36:06 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8069 data points for AZN
2025-05-31 02:36:06 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for AZN
2025-05-31 02:36:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: LIN (market cap: 218977426469.00)
2025-05-31 02:36:07 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for LIN from 1962-01-01 to today
2025-05-31 02:36:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: LIN, from 1962-01-01 to 2025-05-31
2025-05-31 02:36:07 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/LIN/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:36:11 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_LIN_1748630171.html
2025-05-31 02:36:11 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_LIN_1748630171.html
2025-05-31 02:36:11 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 8297 OHLCV data points for LIN
2025-05-31 02:36:13 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 8297 OHLCV data points for LIN
2025-05-31 02:36:13 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8297 data points for LIN
2025-05-31 02:36:13 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for LIN
2025-05-31 02:36:14 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: ACN (market cap: 214617797178.00)
2025-05-31 02:36:14 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Updating data for ACN from 1962-01-01 to today
2025-05-31 02:36:14 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACN, from 1962-01-01 to 2025-05-31
2025-05-31 02:36:14 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACN/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 02:36:17 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACN_1748630177.html
2025-05-31 02:36:17 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACN_1748630177.html
2025-05-31 02:36:17 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceScraper - Extracted 6002 OHLCV data points for ACN
2025-05-31 02:36:19 [http-nio-8080-exec-5] INFO  c.i.database.DatabaseManager - Saved 6002 OHLCV data points for ACN
2025-05-31 02:36:19 [http-nio-8080-exec-5] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6002 data points for ACN
2025-05-31 02:36:19 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for ACN
2025-05-31 02:36:19 [http-nio-8080-exec-5] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 20, Successful: 16, Failed: 4, Skipped: 0
2025-05-31 02:36:19 [http-nio-8080-exec-5] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 20 out of 10415 instruments (range: 0-19). Successfully updated 16 symbols (370592 data points), failed 4 symbols, skipped 0 symbols with recent data.
2025-05-31 02:39:24 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-31 11:51:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 11:51:56 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 26092 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 11:51:56 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 11:51:56 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 11:51:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 11:51:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 11:51:57 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 11:51:57 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 11:51:57 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 11:51:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 519 ms
2025-05-31 11:51:57 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-31 11:51:57 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-31 11:51:57 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-31 11:51:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 11:51:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-31 11:51:57 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.34 seconds (process running for 1.757)
2025-05-31 11:53:07 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 11:53:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 11:53:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-31 11:53:07 [http-nio-8080-exec-1] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: nasdaq_screener_1748616695389.csv, size: 983826 bytes, dryRun: false, maxInstruments: 15000, skipDuplicates: false, validateData: true
2025-05-31 11:53:07 [http-nio-8080-exec-1] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-31 11:53:07 [http-nio-8080-exec-1] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: nasdaq_screener_1748616695389.csv, dryRun: false, maxInstruments: 15000, skipDuplicates: false, validateData: true
2025-05-31 11:53:27 [http-nio-8080-exec-1] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 6898, Valid: 6898, Invalid: 0, Processed: 6898
2025-05-31 11:53:27 [http-nio-8080-exec-1] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 6898 instruments from 6898 valid rows. Added 6898 new instruments, updated 0 existing instruments, skipped 0 duplicates.
2025-05-31 11:53:58 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-31 11:57:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-31 11:57:45 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 19448 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-31 11:57:45 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-31 11:57:45 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-31 11:57:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-31 11:57:45 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 11:57:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 11:57:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-31 11:57:45 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-31 11:57:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 414 ms
2025-05-31 11:57:46 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-31 11:57:46 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-31 11:57:46 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-31 11:57:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 11:57:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-31 11:57:46 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.14 seconds (process running for 1.476)
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=10, skipExisting=true, startIndex=0, endIndex=null}
2025-05-31 11:57:51 [http-nio-8080-exec-1] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Starting OHLCV refresh for all instruments - dryRun: false, maxSymbols: 10, skipExisting: true, startIndex: 0, endIndex: null
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Found 10415 total instruments in database, fetching 10 instruments from index 0 to 9
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: MSFT (market cap: 3412801166492.00)
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for MSFT from 1962-01-01 to today
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: MSFT, from 1962-01-01 to 2025-05-31
2025-05-31 11:57:51 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/MSFT/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:57:57 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MSFT_1748663877.html
2025-05-31 11:57:57 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_MSFT_1748663877.html
2025-05-31 11:57:57 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 9881 OHLCV data points for MSFT
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 9881 OHLCV data points for MSFT
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9881 data points for MSFT
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for MSFT
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: NVDA (market cap: 3349754000000.00)
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for NVDA from 1962-01-01 to today
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: NVDA, from 1962-01-01 to 2025-05-31
2025-05-31 11:57:59 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/NVDA/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:04 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVDA_1748663884.html
2025-05-31 11:58:04 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_NVDA_1748663884.html
2025-05-31 11:58:04 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 6630 OHLCV data points for NVDA
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 6630 OHLCV data points for NVDA
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6630 data points for NVDA
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for NVDA
2025-05-31 11:58:06 [http-nio-8080-exec-1] DEBUG com.investment.service.OHLCVService - Skipping symbol AAPL - has recent data
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AMZN (market cap: 2175577249709.00)
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for AMZN from 1962-01-01 to today
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AMZN, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:06 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AMZN/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:13 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AMZN_1748663893.html
2025-05-31 11:58:13 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AMZN_1748663893.html
2025-05-31 11:58:13 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 7055 OHLCV data points for AMZN
2025-05-31 11:58:14 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 7055 OHLCV data points for AMZN
2025-05-31 11:58:14 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7055 data points for AMZN
2025-05-31 11:58:14 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for AMZN
2025-05-31 11:58:15 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOG (market cap: 2085399750000.00)
2025-05-31 11:58:15 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for GOOG from 1962-01-01 to today
2025-05-31 11:58:15 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: GOOG, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:15 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/GOOG/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:19 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOG_1748663899.html
2025-05-31 11:58:19 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOG_1748663899.html
2025-05-31 11:58:19 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 5229 OHLCV data points for GOOG
2025-05-31 11:58:20 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 5229 OHLCV data points for GOOG
2025-05-31 11:58:20 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5229 data points for GOOG
2025-05-31 11:58:20 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for GOOG
2025-05-31 11:58:21 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: GOOGL (market cap: 2071565850000.00)
2025-05-31 11:58:21 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for GOOGL from 1962-01-01 to today
2025-05-31 11:58:21 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: GOOGL, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:21 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/GOOGL/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:24 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOGL_1748663904.html
2025-05-31 11:58:24 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_GOOGL_1748663904.html
2025-05-31 11:58:24 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 5229 OHLCV data points for GOOGL
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 5229 OHLCV data points for GOOGL
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5229 data points for GOOGL
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for GOOGL
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: META (market cap: 1622093081420.00)
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for META from 1962-01-01 to today
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: META, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:25 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/META/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_META_1748663908.html
2025-05-31 11:58:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_META_1748663908.html
2025-05-31 11:58:28 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 3277 OHLCV data points for META
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 3277 OHLCV data points for META
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3277 data points for META
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for META
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: TSLA (market cap: 1163731479034.00)
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for TSLA from 1962-01-01 to today
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: TSLA, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:29 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/TSLA/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:31 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSLA_1748663911.html
2025-05-31 11:58:31 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_TSLA_1748663911.html
2025-05-31 11:58:31 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 3754 OHLCV data points for TSLA
2025-05-31 11:58:32 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 3754 OHLCV data points for TSLA
2025-05-31 11:58:32 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3754 data points for TSLA
2025-05-31 11:58:32 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for TSLA
2025-05-31 11:58:33 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: AVGO (market cap: 1134791813104.00)
2025-05-31 11:58:33 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for AVGO from 1962-01-01 to today
2025-05-31 11:58:33 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AVGO, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:33 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AVGO/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:36 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AVGO_1748663916.html
2025-05-31 11:58:36 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AVGO_1748663916.html
2025-05-31 11:58:36 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Extracted 3979 OHLCV data points for AVGO
2025-05-31 11:58:37 [http-nio-8080-exec-1] INFO  c.i.database.DatabaseManager - Saved 3979 OHLCV data points for AVGO
2025-05-31 11:58:37 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3979 data points for AVGO
2025-05-31 11:58:37 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for AVGO
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating OHLCV data for symbol: BRK/A (market cap: 1111980744000.00)
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Updating data for BRK/A from 1962-01-01 to today
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: BRK/A, from 1962-01-01 to 2025-05-31
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/BRK/A/history/?period1=-*********&period2=**********&interval=1d
2025-05-31 11:58:38 [http-nio-8080-exec-1] ERROR c.i.provider.YahooFinanceScraper - Failed to save HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748663918.html
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748663918.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 11:58:38 [http-nio-8080-exec-1] WARN  c.i.provider.YahooFinanceScraper - Failed to save HTML content to file, continuing with in-memory content
java.io.FileNotFoundException: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_BRK\A_1748663918.html (The system cannot find the path specified)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:289)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:230)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:179)
	at java.base/java.io.FileWriter.<init>(FileWriter.java:98)
	at com.investment.provider.YahooFinanceScraper.saveHtmlToFile(YahooFinanceScraper.java:85)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:157)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:37)
	at com.investment.service.OHLCVService.updateOHLCVData(OHLCVService.java:80)
	at com.investment.service.OHLCVService.refreshAllOHLCVData(OHLCVService.java:197)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-31 11:58:38 [http-nio-8080-exec-1] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for BRK/A
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for BRK/A
2025-05-31 11:58:38 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - Successfully updated 23162 data points for BRK/A
2025-05-31 11:58:39 [http-nio-8080-exec-1] INFO  com.investment.service.OHLCVService - OHLCV refresh completed - Processed: 9, Successful: 9, Failed: 0, Skipped: 1
2025-05-31 11:58:39 [http-nio-8080-exec-1] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 9 out of 10415 instruments (range: 0-9). Successfully updated 9 symbols (208458 data points), failed 0 symbols, skipped 1 symbols with recent data.
2025-05-31 18:01:47 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
